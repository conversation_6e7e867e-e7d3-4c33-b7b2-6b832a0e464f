/**
 * Exam Session Redux Slice
 * Manages exam session state based on ExamSession API Documentation
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import URL from '../../../utils/api/API_URL';

const API_BASE = `${URL}/api/exams`;
const getAuthToken = () => localStorage.getItem("token");

// Helper functions for submission format
const mapQuestionType = (type) => {
  // Normalize input type to uppercase
  const normalizedType = String(type || '').toUpperCase();

  // Map various question types to backend-accepted values
  const typeMap = {
    // Standard types
    'MCQS': 'MCQS',
    'SHORT': 'SHORT',
    'LONG': 'LONG',

    // Common variations
    'MCQ': 'MCQS',
    'MULTIPLE_CHOICE': 'MCQS',
    'SHORT_ANSWER': 'SHORT',
    'LONG_ANSWER': 'LONG',

    // Handle DESCRIPTIVE type - map to LONG for detailed answers
    'DESCRIPTIVE': 'LONG',
    'DESCRIPTION': 'LONG',
    'ESSAY': 'LONG',

    // Handle other variations
    'TEXT': 'SHORT',
    'TEXTUAL': 'SHORT'
  };

  const mappedType = typeMap[normalizedType];

  // Log mapping for debugging
  if (normalizedType !== mappedType) {
    console.log(`🔄 Question type mapped: ${type} → ${mappedType}`);
  }

  // Default to MCQS if type is not recognized
  return mappedType || 'MCQS';
};

const formatQuestionOptions = (question) => {
  if (question.Type !== 'MCQS' || !question.options) return undefined;

  const options = {};
  question.options.forEach((option, index) => {
    const letter = String.fromCharCode(65 + index); // A, B, C, D
    options[letter] = option.option_text;
  });
  return options;
};

const getCorrectAnswer = (question) => {
  if (question.Type !== 'MCQS' || !question.options) return undefined;

  const correctIndex = question.options.findIndex(option => option.is_correct);
  if (correctIndex === -1) return undefined;

  return String.fromCharCode(65 + correctIndex); // A, B, C, D
};

// Format exam data according to API specification
const formatExamData = (exam) => {

  // Handle missing title field - provide comprehensive fallback
  let title = exam.title || exam.name;

  if (!title) {
    // Try to generate a meaningful title from available data
    const examId = exam.id || exam.exam_id;
    const shortId = examId ? examId.substring(0, 8) : 'Unknown';

    // Check if we have subject or other context
    if (exam.questions && exam.questions.length > 0) {
      const firstQuestion = exam.questions[0];
      if (firstQuestion.subject_name) {
        title = `${firstQuestion.subject_name} Exam (${shortId})`;
      } else if (firstQuestion.chapter_name) {
        title = `${firstQuestion.chapter_name} Exam (${shortId})`;
      } else {
        title = `Exam ${shortId}`;
      }
    } else {
      title = `Exam ${shortId}`;
    }
  }

  const formattedData = {
    exam_id: exam.id || exam.exam_id,
    title: title,
    description: exam.description || null,
    total_marks: exam.total_marks || 0,
    total_duration: exam.total_duration || exam.duration || 0,
    start_time: exam.start_time || exam.startTime || null
  };

  return formattedData;
};

// Format questions data according to API specification
const formatQuestionsData = (questions) => {
  return questions.map(question => ({
    question_id: question.id || question.question_id,
    question_text: question.text || question.question_text,
    Type: mapQuestionType(question.Type || question.type), // ✅ Backend expects 'Type' field
    options: formatQuestionOptions(question),
    marks: question.marks || 1
  }));
};

// Format student answers according to API specification
const formatStudentAnswers = (studentAnswers) => {
  // Handle empty answers (e.g., when student is caught cheating immediately)
  if (!studentAnswers || Object.keys(studentAnswers).length === 0) {
    return [];
  }

  return Object.entries(studentAnswers).map(([questionId, answerData]) => ({
    question_id: questionId,
    answer: typeof answerData === 'string' ? answerData : (answerData.answer || answerData.text || ''),
    time_spent_seconds: typeof answerData === 'object' ? (answerData.time_spent_seconds || 0) : 0
  }));
};

// Async thunks for exam session operations

// Request exam session (correct endpoint)
export const requestExamSession = createAsyncThunk(
  'examSession/request',
  async ({ examId }, { rejectWithValue }) => {
    try {
      const token = getAuthToken();

      if (!token) {
        return rejectWithValue('No authentication token available');
      }

      console.log('🎯 Redux: Requesting exam session for exam:', examId);

      // Use the correct WebSocket API endpoint
      const response = await fetch(`${URL}/exam-session/request/${examId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });



      if (!response.ok) {
        let errorMessage;
        try {
          const error = await response.json();
          errorMessage = error.detail || error.message || 'Failed to start exam session';
        } catch (parseError) {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        return rejectWithValue(errorMessage);
      }

      const data = await response.json();
      return { sessionId: data.session_id, examId };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Submit exam session
export const submitExamSession = createAsyncThunk(
  'examSession/submit',
  async ({ sessionId, exam, questions, studentAnswers, isAutoSubmit = false, timingData = {} }, { rejectWithValue }) => {
    try {
      const token = getAuthToken();

      console.log('📤 Redux: Submitting exam session:', { sessionId, isAutoSubmit });

      // Validate required fields according to API specification
      if (!sessionId) {
        return rejectWithValue('Session ID is required for submission.');
      }

      if (!exam) {
        return rejectWithValue('Exam object is required for submission.');
      }

      if (!questions || questions.length === 0) {
        return rejectWithValue('Questions data is required and cannot be empty.');
      }

      // Format exam data according to API specification
      const formattedExam = {
        exam_id: exam.exam_id || exam.id, // ✅ Handle both data structures
        title: exam.title,
        description: exam.description,
        total_marks: exam.total_marks,
        total_duration: exam.total_duration,
        start_time: exam.start_time
      };

      // Format questions according to API specification
      const formattedQuestions = questions.map(question => {
        const originalType = question.question_type || question.Type || question.type || 'MCQS';
        const mappedType = mapQuestionType(originalType);

        return {
          question_id: question.question_id || question.id, // ✅ Handle both data structures
          question_text: question.question_text || question.text, // ✅ Handle both data structures
          Type: mappedType, // ✅ Use mapped type that backend accepts
          options: question.options || {},
          marks: question.marks || 1
        };
      });

      // Format student answers according to API specification
      // Convert option IDs to answer text for easier backend handling
      const formattedAnswers = Object.entries(studentAnswers || {}).map(([questionId, optionId]) => {
        // Find the question to get the option text
        const question = questions.find(q =>
          (q.question_id || q.id) === questionId
        );
        let answerText = optionId; // fallback to option ID if not found

        if (question && question.options) {
          // Handle both array and object format for options
          if (Array.isArray(question.options)) {
            const selectedOption = question.options.find(opt => opt.id === optionId);
            if (selectedOption) {
              answerText = selectedOption.option_text;
            }
          } else if (typeof question.options === 'object') {
            // Handle object format: { "option_id": { "option_text": "text", "is_correct": false } }
            const selectedOption = question.options[optionId];
            if (selectedOption) {
              answerText = selectedOption.option_text;
            }
          }
        }

        return {
          question_id: questionId,
          answer: answerText, // Use answer text instead of option ID
          time_spent_seconds: timingData[questionId] || 0
        };
      });

      // Prepare submission payload according to API specification
      const submissionPayload = {
        session_id: sessionId,
        exam: formattedExam,
        questions: formattedQuestions,
        student_answers: formattedAnswers
      };

      console.log('📤 Redux: Submission payload:', submissionPayload);
      console.log('🔍 Debug exam data structure:', exam);
      console.log('🔍 Debug questions data structure:', questions);
      console.log('🔍 Debug student answers:', studentAnswers);
      console.log('🔍 Debug timing data:', timingData);
      console.log('🔍 Debug formatted answers:', formattedAnswers);
      console.log('🔍 Debug formatted questions:', formattedQuestions);

      // Use the correct endpoint
      const response = await fetch(`${URL}/exam-session/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(submissionPayload)
      });



      if (!response.ok) {
        let errorMessage;
        try {
          const error = await response.json();
          errorMessage = error.detail || error.message || 'Failed to submit exam session';
        } catch (parseError) {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        return rejectWithValue(errorMessage);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Request reconnection
export const requestReconnection = createAsyncThunk(
  'examSession/requestReconnection',
  async ({ sessionId, reason }, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      const response = await fetch(`${API_BASE}/exam-session/reconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ session_id: sessionId, reason })
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to request reconnection');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Check reconnection status
export const checkReconnectionStatus = createAsyncThunk(
  'examSession/checkReconnectionStatus',
  async ({ requestId }, { getState, rejectWithValue }) => {
    try {
      const { login } = getState();
      const token = login.user?.access_token || localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/exam-session/reconnect/${requestId}/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to check reconnection status');
      }

      const data = await response.json();
      return { requestId, ...data };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Resume exam session
export const resumeExamSession = createAsyncThunk(
  'examSession/resume',
  async ({ sessionId }, { getState, rejectWithValue }) => {
    try {
      const { login } = getState();
      const token = login.user?.access_token || localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/exam-session/${sessionId}/resume`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to resume exam session');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  // Session management
  sessionId: null,
  examId: null,
  status: 'idle', // idle, starting, active, submitting, submitted, disconnected, reconnecting

  // Exam data
  examData: null,
  questions: [],
  answers: {},

  // Session data
  currentAnswers: {},
  remainingTimeSeconds: 0,
  totalDurationSeconds: 0,
  totalQuestions: 0,
  strikes: 0,

  // WebSocket connection
  connectionStatus: 'disconnected', // disconnected, connecting, connected, reconnecting
  lastHeartbeat: null,

  // Reconnection
  reconnectionRequestId: null,
  reconnectionStatus: null, // pending, approved, denied
  reconnectionReason: null,
  teacherReason: null,

  // Loading states
  loading: false,
  submitting: false,
  reconnecting: false,

  // Error handling
  error: null,

  // Submission result
  submissionResult: null,
  isSubmitted: false,
  isSubmitting: false,
  isDisqualified: false,
  disqualificationReason: null,
  autoSubmitTriggered: false,

  // Question navigation
  currentQuestionIndex: 0,

  // Warnings
  warnings: [],
  showWarningModal: false,
  currentWarning: null
};

const examSessionSlice = createSlice({
  name: 'examSession',
  initialState,
  reducers: {
    // WebSocket connection management
    setConnectionStatus: (state, action) => {
      state.connectionStatus = action.payload;
    },

    // Session status updates
    setSessionStatus: (state, action) => {
      state.status = action.payload;
    },

    // Session ID management
    setSessionId: (state, action) => {
      state.sessionId = action.payload;
    },

    // Answer management
    updateAnswer: (state, action) => {
      const { questionId, answer } = action.payload;
      state.currentAnswers[questionId] = answer;
    },

    setCurrentAnswers: (state, action) => {
      state.currentAnswers = action.payload;
    },

    // Timer management
    setRemainingTime: (state, action) => {
      state.remainingTimeSeconds = action.payload;
    },

    decrementTime: (state) => {
      if (state.remainingTimeSeconds > 0) {
        state.remainingTimeSeconds -= 1;
      }
    },

    // Strike management
    addStrike: (state) => {
      state.strikes += 1;

      // Auto-submit when strikes reach 3
      if (state.strikes >= 3) {

        state.isDisqualified = true;
        state.disqualificationReason = 'Maximum cheating violations reached (3/3)';
        state.status = 'terminated';
        // The component should listen for this state change and trigger submission
        state.autoSubmitTriggered = true;
      }
    },

    setStrikes: (state, action) => {
      state.strikes = action.payload;
    },

    // Heartbeat tracking
    updateHeartbeat: (state) => {
      state.lastHeartbeat = new Date().toISOString();
    },

    // Set exam data
    setExamData: (state, action) => {
      state.examData = action.payload.examData;
      state.questions = action.payload.questions || [];
      state.totalQuestions = state.questions.length;
      state.remainingTimeSeconds = action.payload.remainingTime || 0;
    },

    // Clear session data
    clearSession: (state) => {
      state.sessionId = null;
      state.examId = null;
      state.status = 'idle';
      state.examData = null;
      state.questions = [];
      state.answers = {};
      state.currentAnswers = {};
      state.remainingTimeSeconds = 0;
      state.totalDurationSeconds = 0;
      state.totalQuestions = 0;
      state.strikes = 0;
      state.connectionStatus = 'disconnected';
      state.lastHeartbeat = null;
      state.reconnectionRequestId = null;
      state.reconnectionStatus = null;
      state.reconnectionReason = null;
      state.teacherReason = null;
      state.error = null;
      state.submissionResult = null;
      state.isSubmitted = false;
      state.isSubmitting = false;
      state.isDisqualified = false;
      state.disqualificationReason = null;
      state.autoSubmitTriggered = false;
      state.currentQuestionIndex = 0;
      state.warnings = [];
      state.showWarningModal = false;
      state.currentWarning = null;
    },

    // Error handling
    clearError: (state) => {
      state.error = null;
    },

    // Question navigation
    setCurrentQuestion: (state, action) => {
      state.currentQuestionIndex = action.payload;
    },

    // WebSocket message handling
    handleWebSocketMessage: (state, action) => {
      const { type, payload } = action.payload;

      switch (type) {
        case 'timer_update':
          state.remainingTimeSeconds = payload.remaining_time;
          break;
        case 'answer_saved':
          // Answer was saved successfully on server
          break;
        case 'violation_detected':
          state.strikes += 1;
          break;
        case 'violation_warning':
          // Handle violation warnings from WebSocket
          if (payload.strikes !== undefined) {
            state.strikes = payload.strikes;
          } else {
            // If strikes not provided, increment manually
            state.strikes += 1;
          }

          // Auto-submit when strikes reach 3
          if (state.strikes >= 3) {
            state.isDisqualified = true;
            state.disqualificationReason = 'Maximum cheating violations reached (3/3)';
            state.status = 'terminated';
            state.autoSubmitTriggered = true;
          }
          break;
        case 'session_terminated':
          state.status = 'terminated';
          state.isDisqualified = true;
          state.disqualificationReason = payload.reason;
          break;
        default:

      }
    },

    // Student disqualification
    disqualifyStudent: (state, action) => {
      state.isDisqualified = true;
      state.disqualificationReason = action.payload.reason;
      state.status = 'terminated';
    },

    // Warning management
    dismissWarning: (state) => {
      state.showWarningModal = false;
      state.currentWarning = null;
    }
  },

  extraReducers: (builder) => {
    // Request exam session (correct endpoint)
    builder
      .addCase(requestExamSession.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.status = 'starting';
        console.log('🎯 Redux: Requesting exam session...');
      })
      .addCase(requestExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.sessionId = action.payload.sessionId;
        state.examId = action.payload.examId;
        state.status = 'active';
        console.log('✅ Redux: Exam session requested successfully:', action.payload);
      })
      .addCase(requestExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.status = 'idle';
        console.error('❌ Redux: Failed to request exam session:', action.payload);
      });

    // Submit exam session
    builder
      .addCase(submitExamSession.pending, (state) => {
        state.isSubmitting = true;
        state.submitting = true;
        state.error = null;
        state.status = 'submitting';
      })
      .addCase(submitExamSession.fulfilled, (state, action) => {
        state.isSubmitting = false;
        state.submitting = false;
        state.isSubmitted = true;
        state.status = 'submitted';
        state.submissionResult = action.payload;
        state.isDisqualified = action.payload.disqualified || false;
        state.disqualificationReason = action.payload.disqualification_reason;
      })
      .addCase(submitExamSession.rejected, (state, action) => {
        state.isSubmitting = false;
        state.submitting = false;
        state.error = action.payload;
        state.status = 'active';
      });

    // Request reconnection
    builder
      .addCase(requestReconnection.pending, (state) => {
        state.reconnecting = true;
        state.error = null;
      })
      .addCase(requestReconnection.fulfilled, (state, action) => {
        state.reconnecting = false;
        state.reconnectionRequestId = action.payload.request_id;
        state.reconnectionStatus = action.payload.status;
      })
      .addCase(requestReconnection.rejected, (state, action) => {
        state.reconnecting = false;
        state.error = action.payload;
      });

    // Check reconnection status
    builder
      .addCase(checkReconnectionStatus.fulfilled, (state, action) => {
        state.reconnectionStatus = action.payload.status;
        state.teacherReason = action.payload.teacher_reason;
        if (action.payload.session_id) {
          state.sessionId = action.payload.session_id;
        }
      });

    // Resume exam session
    builder
      .addCase(resumeExamSession.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.status = 'reconnecting';
      })
      .addCase(resumeExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.sessionId = action.payload.session_id;
        state.examId = action.payload.exam_id;
        state.currentAnswers = action.payload.current_answers;
        state.remainingTimeSeconds = action.payload.remaining_time_seconds;
        state.totalDurationSeconds = action.payload.total_duration_seconds;
        state.strikes = action.payload.strikes;
        state.status = action.payload.status;
      })
      .addCase(resumeExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.status = 'disconnected';
      });
  }
});

export const {
  setConnectionStatus,
  setSessionStatus,
  setSessionId,
  updateAnswer,
  setCurrentAnswers,
  setRemainingTime,
  decrementTime,
  addStrike,
  setStrikes,
  updateHeartbeat,
  setExamData,
  clearSession,
  clearError,
  setCurrentQuestion,
  handleWebSocketMessage,
  disqualifyStudent,
  dismissWarning
} = examSessionSlice.actions;

// Selectors
export const selectExamSession = (state) => state.examSession;
export const selectCurrentQuestion = (state) => {
  const session = state.examSession;
  const currentIndex = session.currentQuestionIndex || 0;
  return session.questions[currentIndex] || null;
};
export const selectProgress = (state) => {
  const session = state.examSession;
  const totalQuestions = session.totalQuestions || session.questions?.length || 0;
  const answeredCount = Object.keys(session.currentAnswers || {}).length;
  return {
    current: session.currentQuestionIndex || 0,
    total: totalQuestions,
    answered: answeredCount,
    percentage: totalQuestions > 0 ? Math.round((answeredCount / totalQuestions) * 100) : 0
  };
};

// Export helper functions for testing
export {
  mapQuestionType,
  formatQuestionOptions,
  getCorrectAnswer,
  formatExamData,
  formatQuestionsData,
  formatStudentAnswers
};

export default examSessionSlice.reducer;
