import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FiHome,
  FiClock,
  <PERSON>Check,
  FiX,
  FiAlertCircle,
  FiSettings,
  FiArrowRight
} from 'react-icons/fi';
import {
  fetchInstituteProfile,
  selectProfile,
  selectProfileLoading,
  selectProfileError,
  selectProfileNotFound,
  selectApprovalStatus,
  selectIsProfileComplete,
  selectRejectionReason
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../ui';

const InstituteApprovalGuard = ({ children }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Redux state
  const profile = useSelector(selectProfile);
  const profileLoading = useSelector(selectProfileLoading);
  const profileError = useSelector(selectProfileError);
  const profileNotFound = useSelector(selectProfileNotFound);
  const approvalStatus = useSelector(selectApprovalStatus);
  const isProfileComplete = useSelector(selectIsProfileComplete);
  const rejectionReason = useSelector(selectRejectionReason);

  // Local state
  const [isChecking, setIsChecking] = useState(true);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);

  // Check if current route is settings
  const isSettingsRoute = location.pathname.includes('/settings');

  useEffect(() => {
    const checkApprovalStatus = async () => {
      // Don't fetch again if we already attempted
      if (hasAttemptedFetch) {
        setIsChecking(false);
        return;
      }

      try {
        setHasAttemptedFetch(true);
        await dispatch(fetchInstituteProfile()).unwrap();
      } catch (error) {
        // Error is already handled in the Redux slice
        console.log('Profile fetch failed:', error);
      } finally {
        setIsChecking(false);
      }
    };

    checkApprovalStatus();
  }, [dispatch]); // Only depend on dispatch - run once on mount

  // Show loading while checking
  if (isChecking || profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // If user is on settings page, always allow access
  if (isSettingsRoute) {
    return children;
  }

  // Check approval status and redirect accordingly
  const renderApprovalStatus = () => {
    switch (approvalStatus) {
      case 'not_created':
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="mb-6">
                <FiHome className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Create Your Institute Profile
                </h1>
                <p className="text-gray-600">
                  Your institute profile is missing. Please create your profile to get started.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <FiAlertCircle className="h-5 w-5 text-blue-600 mr-3" />
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-blue-800">
                      Profile Required
                    </h3>
                    <p className="text-sm text-blue-700 mt-1">
                      You need to create your institute profile before you can access the platform features.
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => navigate('/institute/settings')}
                className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiSettings className="h-5 w-5 mr-2" />
                Create Profile
                <FiArrowRight className="h-5 w-5 ml-2" />
              </button>
            </div>
          </div>
        );

      case 'draft':
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="mb-6">
                <FiHome className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Complete Your Institute Profile
                </h1>
                <p className="text-gray-600">
                  Please complete your institute profile to get admin approval and access all features.
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <FiAlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Profile Incomplete
                    </h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      {!isProfileComplete 
                        ? 'Fill in all required fields to submit for approval.'
                        : 'Your profile is complete but not yet submitted for approval.'
                      }
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => navigate('/institute/settings')}
                className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiSettings className="h-5 w-5 mr-2" />
                Complete Profile
                <FiArrowRight className="h-5 w-5 ml-2" />
              </button>
            </div>
          </div>
        );

      case 'pending':
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="mb-6">
                <FiClock className="h-16 w-16 text-yellow-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Approval Pending
                </h1>
                <p className="text-gray-600">
                  Your institute profile has been submitted and is awaiting admin approval.
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <FiClock className="h-5 w-5 text-yellow-600 mr-3" />
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Under Review
                    </h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Our admin team is reviewing your profile. You'll be notified once approved.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => navigate('/institute/settings')}
                  className="w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FiSettings className="h-5 w-5 mr-2" />
                  View Profile
                </button>
                
                <p className="text-xs text-gray-500">
                  Approval typically takes 1-2 business days
                </p>
              </div>
            </div>
          </div>
        );

      case 'rejected':
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="mb-6">
                <FiX className="h-16 w-16 text-red-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Profile Rejected
                </h1>
                <p className="text-gray-600">
                  Your institute profile was not approved. Please review the feedback and resubmit.
                </p>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <FiAlertCircle className="h-5 w-5 text-red-600 mr-3 mt-0.5" />
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-red-800">
                      Rejection Reason
                    </h3>
                    <p className="text-sm text-red-700 mt-1">
                      {rejectionReason || 'No specific reason provided.'}
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => navigate('/institute/settings')}
                className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <FiSettings className="h-5 w-5 mr-2" />
                Update Profile
                <FiArrowRight className="h-5 w-5 ml-2" />
              </button>
            </div>
          </div>
        );

      case 'approved':
        // Allow access to the application
        return children;

      default:
        // Unknown status, redirect to settings
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="mb-6">
                <FiAlertCircle className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Setup Required
                </h1>
                <p className="text-gray-600">
                  Please set up your institute profile to continue.
                </p>
              </div>

              <button
                onClick={() => navigate('/institute/settings')}
                className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiSettings className="h-5 w-5 mr-2" />
                Setup Profile
                <FiArrowRight className="h-5 w-5 ml-2" />
              </button>
            </div>
          </div>
        );
    }
  };

  return renderApprovalStatus();
};

export default InstituteApprovalGuard;
