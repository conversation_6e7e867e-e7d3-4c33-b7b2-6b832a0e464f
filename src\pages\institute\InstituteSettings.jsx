import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchInstituteProfile,
  fetchProfileStatus,
  saveInstituteProfile,
  saveInstituteProfileWithDocuments,
  submitForApproval,
  selectProfile,
  selectProfileLoading,
  selectProfileError,
  selectProfileNotFound,
  selectSaveLoading,
  selectSaveError,
  selectSaveSuccess,
  selectSubmitLoading,
  selectSubmitError,
  selectSubmitSuccess,
  selectApprovalStatus,
  selectIsProfileComplete,
  selectRejectionReason,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';

// Import profile components
import InstituteProfileHeader from '../../components/institute/profile/InstituteProfileHeader';
import InstituteBasicInfoForm from '../../components/institute/profile/InstituteBasicInfoForm';
import InstituteContactForm from '../../components/institute/profile/InstituteContactForm';
import InstituteSocialLinksForm from '../../components/institute/profile/InstituteSocialLinksForm';
import InstituteProfileActions from '../../components/institute/profile/InstituteProfileActions';
import InstituteDocumentSection from '../../components/institute/profile/InstituteDocumentSection';
import VerificationStatus from '../../components/institute/VerificationStatus';
import { getErrorMessage } from '../../utils/helpers/errorHandler';
import { hasDocumentChanges, getValidDocumentsForUpload } from '../../utils/documentHelpers';
import {
  validateInstituteForm,
  validateSingleField,
  validateImageFile,
  fileToBase64,
  VALID_INSTITUTE_TYPES
} from '../../utils/validation/instituteValidation';

const InstituteSettings = () => {
  const dispatch = useDispatch();
  const hasFetchedRef = useRef(false);

  // Redux state
  const profile = useSelector(selectProfile);
  const profileLoading = useSelector(selectProfileLoading);
  const profileError = useSelector(selectProfileError);
  const profileNotFound = useSelector(selectProfileNotFound);
  const saveLoading = useSelector(selectSaveLoading);
  const saveError = useSelector(selectSaveError);
  const saveSuccess = useSelector(selectSaveSuccess);
  const submitLoading = useSelector(selectSubmitLoading);
  const submitError = useSelector(selectSubmitError);
  const submitSuccess = useSelector(selectSubmitSuccess);
  const approvalStatus = useSelector(selectApprovalStatus);
  const isProfileComplete = useSelector(selectIsProfileComplete);
  const rejectionReason = useSelector(selectRejectionReason);

  // Local state
  const [formData, setFormData] = useState({
    institute_name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    website: '',
    established_year: '',
    institute_type: '',
    accreditation: '',
    linkedin_url: '',
    facebook_url: '',
    twitter_url: '',
    logo: null,
    banner: null
  });

  const [isEditing, setIsEditing] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [originalDocuments, setOriginalDocuments] = useState([]);
  const [fieldErrors, setFieldErrors] = useState({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [originalFormData, setOriginalFormData] = useState({});

  // Mandatory fields for validation
  const mandatoryFields = {
    institute_name: 'Institute Name',
    institute_type: 'Institute Type',
    description: 'Description',
    address: 'Address',
    city: 'City',
    state: 'State',
    documents: 'Verification Documents'
  };

  // Load profile on component mount - only once
  useEffect(() => {
    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      dispatch(fetchInstituteProfile());
    }
  }, [dispatch]);

  // Auto-enable editing mode when profile is not found (create mode)
  useEffect(() => {
    if (profileNotFound) {
      setIsEditing(true);
    }
  }, [profileNotFound]);

  // Update form data when profile is loaded
  useEffect(() => {
    if (profile) {
      const profileData = {
        institute_name: profile.institute_name || '',
        description: profile.description || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        postal_code: profile.postal_code || '',
        website: profile.website || '',
        established_year: profile.established_year || '',
        institute_type: profile.institute_type || '',
        accreditation: profile.accreditation || '',
        linkedin_url: profile.linkedin_url || '',
        facebook_url: profile.facebook_url || '',
        twitter_url: profile.twitter_url || '',
        logo: profile.logo || null,
        banner: profile.banner || null
      };
      
      setFormData(profileData);
      setOriginalFormData(profileData);
      
      // Set documents from profile
      if (profile.documents) {
        setDocuments(profile.documents);
        setOriginalDocuments(profile.documents);
      }
    }
  }, [profile]);

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
      dispatch(clearSuccessStates());
    };
  }, [dispatch]);

  // Validation functions
  const validateForm = () => {
    const errors = validateInstituteForm(formData, mandatoryFields);

    // Validate documents
    if (documents.length === 0) {
      errors.documents = 'At least one verification document is required';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const isFormValidForSubmit = () => {
    return Object.keys(mandatoryFields).every(field => {
      if (field === 'documents') {
        return documents.length > 0;
      }
      return formData[field] && formData[field].toString().trim() !== '';
    });
  };

  const hasUnsavedChanges = () => {
    const formChanged = JSON.stringify(formData) !== JSON.stringify(originalFormData);
    const documentsChanged = hasDocumentChanges(documents, originalDocuments);
    return formChanged || documentsChanged;
  };

  // Event handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation for the field
    const fieldError = validateSingleField(name, value, formData, mandatoryFields);
    setFieldErrors(prev => ({
      ...prev,
      [name]: fieldError
    }));
  };

  // Handle file uploads for logo and banner
  const handleFileChange = async (e, fieldName) => {
    const file = e.target.files[0];

    if (!file) {
      // File was cleared
      setFormData(prev => ({
        ...prev,
        [fieldName]: null
      }));
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: null
      }));
      return;
    }

    // Validate the file
    const fileError = validateImageFile(file, fieldName);
    if (fileError) {
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: fileError
      }));
      return;
    }

    try {
      // Convert file to base64 format for API
      const fileData = await fileToBase64(file);

      setFormData(prev => ({
        ...prev,
        [fieldName]: fileData
      }));

      // Clear any previous errors
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: null
      }));
    } catch (error) {
      console.error('Error processing file:', error);
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: 'Error processing file. Please try again.'
      }));
    }
  };

  const handleToggleEdit = () => {
    if (isEditing) {
      // Cancel editing - reset form data and documents
      setFormData(originalFormData);
      setDocuments(originalDocuments);
      setFieldErrors({});
      setHasAttemptedSubmit(false);
    }
    setIsEditing(!isEditing);
  };

  const handleCancelEdit = () => {
    setFormData(originalFormData);
    setDocuments(originalDocuments);
    setFieldErrors({});
    setHasAttemptedSubmit(false);
    setIsEditing(false);
  };

  const handleSave = async () => {
    setHasAttemptedSubmit(true);

    if (profileNotFound && !validateForm()) {
      return;
    }

    try {
      const newDocuments = getValidDocumentsForUpload(documents);

      // Use appropriate endpoint based on whether there are new documents
      if (newDocuments.length > 0) {
        await dispatch(saveInstituteProfileWithDocuments({
          profileData: formData,
          documents: newDocuments
        })).unwrap();
      } else {
        await dispatch(saveInstituteProfile(formData)).unwrap();
      }

      // Update original state after successful save
      setOriginalFormData(formData);
      setOriginalDocuments(documents);
      setHasAttemptedSubmit(false);
      setFieldErrors({});
    } catch (error) {
      console.error('Failed to save profile:', error);
    }
  };

  const handleSubmit = async () => {
    setHasAttemptedSubmit(true);

    if (!validateForm()) {
      return;
    }

    try {
      // Save first, then submit
      await handleSave();
      await dispatch(submitForApproval()).unwrap();
    } catch (error) {
      console.error('Failed to submit for approval:', error);
    }
  };

  // Loading state
  if (profileLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Error state - but only show error if it's not a "profile not found" case
  if (profileError && !profileNotFound) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <p>Error loading profile: {getErrorMessage(profileError)}</p>
        </div>
        <button
          onClick={() => dispatch(fetchInstituteProfile())}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Verification Status - Show for existing profiles */}
      {!profileNotFound && (
        <VerificationStatus 
          showRefreshButton={true}
          className="mb-6"
        />
      )}

      {/* Profile Header */}
      <InstituteProfileHeader
        profile={profile}
        profileNotFound={profileNotFound}
        approvalStatus={approvalStatus}
        isEditing={isEditing}
        onToggleEdit={handleToggleEdit}
        onCancelEdit={handleCancelEdit}
      />

      {/* Basic Information Form */}
      <InstituteBasicInfoForm
        formData={formData}
        onChange={handleInputChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        mandatoryFields={mandatoryFields}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Contact Information Form */}
      <InstituteContactForm
        formData={formData}
        onChange={handleInputChange}
        onFileChange={handleFileChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        mandatoryFields={mandatoryFields}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Social Links Form */}
      <InstituteSocialLinksForm
        formData={formData}
        onChange={handleInputChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Documents Section */}
      <InstituteDocumentSection
        documents={documents}
        onDocumentsChange={setDocuments}
        isEditing={isEditing}
        profileNotFound={profileNotFound}
      />

      {/* Profile Actions */}
      <InstituteProfileActions
        isEditing={isEditing}
        profileNotFound={profileNotFound}
        approvalStatus={approvalStatus}
        saveLoading={saveLoading}
        submitLoading={submitLoading}
        saveSuccess={saveSuccess}
        submitSuccess={submitSuccess}
        isFormValid={isFormValidForSubmit()}
        hasUnsavedChanges={hasUnsavedChanges()}
        onSave={handleSave}
        onSubmit={handleSubmit}
        onToggleEdit={handleToggleEdit}
        onCancelEdit={handleCancelEdit}
      />
    </div>
  );
};

export default InstituteSettings;
