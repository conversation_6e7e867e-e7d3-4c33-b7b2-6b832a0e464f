/**
 * useExamSession Hook
 * Custom hook for managing exam session state and operations
 */

import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import ExamWebSocketService, { WS_EVENTS } from '../../services/exam/websocket/ExamWebSocketService';
import AntiCheatService from '../../services/exam/security/AntiCheatService';
import {
  requestExamSession,
  submitExamSession,
  updateAnswer,
  setConnectionStatus,
  addStrike,
  disqualifyStudent,
  handleWebSocketMessage,
  setRemainingTime,
  selectExamSession
} from '../../store/slices/exam/examSessionSlice';

export const useExamSession = (examId) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const examSession = useSelector(selectExamSession);
  
  // Refs for cleanup
  const wsListenersRef = useRef(new Set());
  const antiCheatActiveRef = useRef(false);
  
  // Get auth token
  const token = localStorage.getItem('token');

  /**
   * Initialize exam session
   */
  const initializeSession = useCallback(async () => {
    if (!examId || !token) {
      throw new Error('Missing exam ID or authentication token');
    }

    try {
      const result = await dispatch(requestExamSession({
        examId
      })).unwrap();

      return result;
      
    } catch (error) {
      console.error('Failed to initialize exam session:', error);
      throw error;
    }
  }, [examId, token, dispatch]);

  /**
   * Setup WebSocket event listeners
   */
  const setupWebSocketListeners = useCallback(() => {
    // Clear existing listeners
    ExamWebSocketService.removeAllListeners();
    wsListenersRef.current.clear();

    // Connection events
    const onConnected = () => {
      dispatch(setConnectionStatus('connected'));
    };

    const onDisconnected = (data) => {
      dispatch(setConnectionStatus('disconnected'));
    };

    const onReconnecting = (data) => {
      dispatch(setConnectionStatus('reconnecting'));
    };

    // Message handling
    const onMessageReceived = (message) => {
      dispatch(handleWebSocketMessage(message));
    };

    // Security events
    const onDisqualified = (data) => {
      dispatch(disqualifyStudent({ reason: data.reason }));
      
      // Cleanup and redirect
      cleanup();
      setTimeout(() => {
        navigate('/student/exam/disqualified', {
          state: { reason: data.reason }
        });
      }, 3000);
    };

    // Error handling
    const onError = (error) => {
      console.error('WebSocket error:', error);
      
      if (error.code === 'POLICY_VIOLATION') {
        cleanup();
        navigate('/student/dashboard', {
          state: { error: 'Session invalid or unauthorized' }
        });
      }
    };

    // Add listeners
    ExamWebSocketService.on(WS_EVENTS.CONNECTED, onConnected);
    ExamWebSocketService.on(WS_EVENTS.DISCONNECTED, onDisconnected);
    ExamWebSocketService.on(WS_EVENTS.RECONNECTING, onReconnecting);
    ExamWebSocketService.on(WS_EVENTS.MESSAGE_RECEIVED, onMessageReceived);
    ExamWebSocketService.on(WS_EVENTS.DISQUALIFIED, onDisqualified);
    ExamWebSocketService.on(WS_EVENTS.ERROR, onError);

    // Track listeners for cleanup
    wsListenersRef.current.add(WS_EVENTS.CONNECTED);
    wsListenersRef.current.add(WS_EVENTS.DISCONNECTED);
    wsListenersRef.current.add(WS_EVENTS.RECONNECTING);
    wsListenersRef.current.add(WS_EVENTS.MESSAGE_RECEIVED);
    wsListenersRef.current.add(WS_EVENTS.DISQUALIFIED);
    wsListenersRef.current.add(WS_EVENTS.ERROR);
  }, [dispatch, navigate]);

  /**
   * Setup anti-cheating measures
   */
  const setupAntiCheating = useCallback(() => {
    if (antiCheatActiveRef.current) {
      console.warn('Anti-cheat already active');
      return;
    }

    // Activate anti-cheat service
    AntiCheatService.activate();
    antiCheatActiveRef.current = true;

    // Override answer analysis to integrate with Redux
    const originalAnalyze = AntiCheatService.analyzeAnswerPattern;
    AntiCheatService.analyzeAnswerPattern = (questionId, answer) => {
      originalAnalyze(questionId, answer);
      // Additional analysis can be added here
    };
  }, []);

  /**
   * Handle answer updates
   */
  const updateQuestionAnswer = useCallback((questionId, answer) => {
    // Update Redux state
    dispatch(updateAnswer({ questionId, answer }));
    
    // Analyze for cheating patterns
    if (AntiCheatService.isServiceActive()) {
      AntiCheatService.analyzeAnswerPattern(questionId, answer);
    }
    
    // Auto-save after a delay
    setTimeout(() => {
      if (ExamWebSocketService.isConnected()) {
        ExamWebSocketService.syncAnswers(examSession.answers);
      }
    }, 2000);
  }, [dispatch, examSession.answers]);

  /**
   * Submit exam with complete data according to API specification
   */
  const submitExamSessionHook = useCallback(async (exam, questions) => {
    if (!examSession.sessionId || examSession.isSubmitting) {
      return;
    }

    try {


      // Final answer sync
      if (ExamWebSocketService.isConnected()) {
        ExamWebSocketService.syncAnswers(examSession.answers);
        // Wait a moment for sync
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Submit exam with complete data according to API specification
      const result = await dispatch(submitExamSession({
        sessionId: examSession.sessionId,
        exam: exam,
        questions: questions || [],
        studentAnswers: examSession.answers || {}
      })).unwrap();

      // Cleanup
      cleanup();


      return result;

    } catch (error) {
      console.error('Failed to submit exam:', error);
      throw error;
    }
  }, [examSession, dispatch]);

  /**
   * Handle timer updates
   */
  const handleTimerUpdate = useCallback((remainingSeconds) => {
    dispatch(setRemainingTime(remainingSeconds));
    
    // Auto-submit when time expires
    if (remainingSeconds <= 0 && !examSession.isSubmitted) {
      // Note: Auto-submit would need exam and questions data from parent component
      console.warn('Auto-submit requires exam and questions data from parent component');
    }
  }, [dispatch, examSession.isSubmitted, submitExamSession]);

  /**
   * Cleanup function
   */
  const cleanup = useCallback(() => {
    // Remove WebSocket listeners
    ExamWebSocketService.removeAllListeners();
    wsListenersRef.current.clear();

    // Deactivate anti-cheat
    if (antiCheatActiveRef.current) {
      AntiCheatService.deactivate();
      antiCheatActiveRef.current = false;
    }

    // Disconnect WebSocket
    ExamWebSocketService.disconnect();
  }, []);

  /**
   * Setup session when sessionId is available
   */
  useEffect(() => {
    if (examSession.status === 'active' && examSession.sessionId) {
      setupWebSocketListeners();
      setupAntiCheating();
    }
  }, [examSession.status, examSession.sessionId, setupWebSocketListeners, setupAntiCheating]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  /**
   * Handle page unload
   */
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (examSession.status === 'active' && !examSession.isSubmitted) {
        e.preventDefault();
        e.returnValue = 'Are you sure you want to leave? Your exam progress may be lost.';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [examSession.status, examSession.isSubmitted]);

  return {
    // State
    examSession,

    // Actions
    initializeSession,
    updateQuestionAnswer,
    submitExamSession: submitExamSessionHook,
    handleTimerUpdate,
    cleanup,

    // Utilities
    isConnected: ExamWebSocketService.isConnected(),
    connectionStatus: examSession.connectionStatus,
    isAntiCheatActive: antiCheatActiveRef.current
  };
};

export default useExamSession;
