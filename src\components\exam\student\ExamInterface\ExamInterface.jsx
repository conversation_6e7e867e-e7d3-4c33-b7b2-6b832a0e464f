/**
 * Main Student Exam Interface
 * Comprehensive exam taking interface with real-time features
 */

import { useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FiShield,
  FiAlertTriangle,
  FiSave,
  FiSend,
  FiAward
} from 'react-icons/fi';

// Import services and components
import ExamWebSocketService, { WS_EVENTS } from '../../../../services/exam/websocket/ExamWebSocketService';
import AntiCheatService from '../../../../services/exam/security/AntiCheatService';

// Enhanced answer management
import AnswerManager from '../../../../utils/exam/AnswerManager';
import {
  requestExamSession,
  submitExamSession,
  updateAnswer,
  setConnectionStatus,
  disqualifyStudent,
  handleWebSocketMessage,
  setRemainingTime,
  dismissWarning,
  setCurrentQuestion,
  setExamData,
  selectExamSession,
  selectCurrentQuestion,
  selectProgress
} from '../../../../store/slices/exam/examSessionSlice';
import { checkExamWithAI } from '../../../../store/slices/exam/aiCheckingSlice';

// Import child components
import QuestionDisplay from '../QuestionDisplay/QuestionDisplay';
import ExamNavigation from '../ExamNavigation/ExamNavigation';
import ExamTimer from '../ExamTimer/ExamTimer';
import ConnectionStatus from '../../../shared/ConnectionStatus/ConnectionStatus';
import SecurityWarnings from '../SecurityWarnings/SecurityWarnings';

const ExamInterface = ({
  exam = null,
  onSubmit = null,
  onAnswerChange = null,
  competitionMode = false,
  competitionEvent = null
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { examId } = useParams();
  
  // Redux state
  const examSession = useSelector(selectExamSession);
  const currentQuestion = useSelector(selectCurrentQuestion);
  const progress = useSelector(selectProgress);
  
  // Local state
  const [isInitialized, setIsInitialized] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState(null);

  // Enhanced answer management
  const [answerManager] = useState(() => new AnswerManager());

  // Get auth token
  const token = localStorage.getItem('token');

  /**
   * Initialize exam session
   */
  const initializeExam = useCallback(async () => {
    if (!examId || !token || isInitialized) return;

    try {
      // If exam data is provided as prop, set it in Redux state
      if (exam) {
        dispatch(setExamData({
          examData: exam,
          questions: exam.questions || [],
          remainingTime: exam.remaining_time_seconds || 0
        }));
      }

      // Request exam session
      await dispatch(requestExamSession({
        examId
      })).unwrap();

      // Initialize answer manager with existing answers
      answerManager.initialize(examSession.answers || {});

      // Set up auto-save callback
      answerManager.setAutoSaveCallback((answers) => {
        if (ExamWebSocketService.isConnected()) {
          ExamWebSocketService.syncAnswers(answers);
          setLastSaveTime(Date.now());
        }
      });

      setIsInitialized(true);


    } catch (error) {

      navigate('/student/dashboard', {
        state: { error: 'Failed to start exam session' }
      });
    }
  }, [examId, token, dispatch, navigate, isInitialized, exam, examSession.answers, answerManager]);

  /**
   * Setup WebSocket event listeners
   */
  const setupWebSocketListeners = useCallback(() => {
    // Connection events
    ExamWebSocketService.on(WS_EVENTS.CONNECTED, () => {

      dispatch(setConnectionStatus('connected'));
    });

    ExamWebSocketService.on(WS_EVENTS.DISCONNECTED, () => {
      dispatch(setConnectionStatus('disconnected'));
    });

    ExamWebSocketService.on(WS_EVENTS.RECONNECTING, () => {
      dispatch(setConnectionStatus('reconnecting'));
    });

    // Message handling
    ExamWebSocketService.on(WS_EVENTS.MESSAGE_RECEIVED, (message) => {
      dispatch(handleWebSocketMessage(message));

      if (message.type === 'answers_saved') {
        setLastSaveTime(Date.now());
      }
    });

    // Violation warning handling
    ExamWebSocketService.on(WS_EVENTS.VIOLATION_WARNING, (data) => {
      console.log('🚨 Violation warning received:', data);

      // Dispatch as WebSocket message to update strikes
      dispatch(handleWebSocketMessage({
        type: 'violation_warning',
        payload: {
          strikes: data.strikes,
          violation_type: data.violationType,
          message: data.message,
          warning: data.warning,
          strikes_remaining: data.strikesRemaining
        }
      }));
    });

    // Security events
    ExamWebSocketService.on(WS_EVENTS.DISQUALIFIED, (data) => {

      dispatch(disqualifyStudent({ reason: data.reason }));

      // Redirect to disqualification page
      setTimeout(() => {
        navigate('/student/exam/disqualified', {
          state: { reason: data.reason }
        });
      }, 3000);
    });

    // Error handling
    ExamWebSocketService.on(WS_EVENTS.ERROR, (error) => {
      console.log('🔌 WebSocket error in ExamInterface:', error);

      if (error.code === 'MAX_RECONNECT_ATTEMPTS') {
        console.log('🚨 Max reconnection attempts reached');
        // Only auto-submit if exam has actually started and student has answered questions
        const hasAnswers = Object.keys(examSession.answers || {}).length > 0;
        if (examSession.status === 'active' && hasAnswers) {
          console.log('Auto-submitting exam due to permanent connection loss');
          setTimeout(() => {
            handleSubmitExam(true); // true = auto-submit
          }, 2000);
        } else {
          console.log('No answers to submit, redirecting to dashboard');
          navigate('/student/dashboard', {
            state: { error: 'Connection lost before exam could be completed' }
          });
        }
      } else if (error.code === 'POLICY_VIOLATION') {
        navigate('/student/dashboard', {
          state: { error: 'Session invalid or unauthorized' }
        });
      }
    });
  }, [dispatch, navigate]);

  /**
   * Setup anti-cheating measures
   */
  const setupAntiCheating = useCallback(() => {
    // Activate anti-cheat service
    AntiCheatService.activate();

    // Mark that the exam has actually started (student is in exam room)
    AntiCheatService.setExamStarted(true);

    // Override the answer analysis function
    const originalAnalyze = AntiCheatService.analyzeAnswerPattern;
    AntiCheatService.analyzeAnswerPattern = (questionId, answer) => {
      originalAnalyze(questionId, answer);

      // Additional custom analysis can be added here

    };


  }, []);

  /**
   * Handle answer updates with enhanced timing tracking
   */
  const handleAnswerUpdate = useCallback((questionId, answer) => {
    // Use custom onAnswerChange handler for competition mode
    if (competitionMode && onAnswerChange) {
      onAnswerChange(questionId, answer);
      return;
    }

    // Save answer with timing data using enhanced answer manager
    const enhancedAnswer = answerManager.saveAnswer(questionId, answer, true);

    // Update Redux state with enhanced answer
    dispatch(updateAnswer({ questionId, answer: enhancedAnswer.answer }));

    // Analyze for cheating patterns
    if (AntiCheatService.isServiceActive()) {
      AntiCheatService.analyzeAnswerPattern(questionId, answer);
    }


  }, [dispatch, answerManager]);

  /**
   * Handle question navigation with timing tracking
   */
  const handleQuestionNavigation = useCallback((questionId) => {
    // Start timing for the new question
    answerManager.navigateToQuestion(questionId);

    // Update current question in Redux
    dispatch(setCurrentQuestion(questionId));


  }, [dispatch, answerManager]);

  /**
   * Handle exam submission
   */
  const handleSubmitExam = useCallback(async (isAutoSubmit = false) => {
    if (!examSession.sessionId || examSession.isSubmitting) return;

    // Use custom onSubmit handler for competition mode
    if (competitionMode && onSubmit) {
      return onSubmit(isAutoSubmit);
    }

    try {


      // Prepare enhanced answers for submission
      const submissionData = answerManager.prepareForSubmission();

      // Final answer sync with enhanced data
      if (ExamWebSocketService.isConnected()) {
        ExamWebSocketService.syncAnswers(submissionData.answers);
      }

      // Submit exam with complete data according to API specification
      const result = await dispatch(submitExamSession({
        sessionId: examSession.sessionId,
        exam: examSession.examData,
        questions: examSession.questions || [],
        studentAnswers: submissionData.answers,
        isAutoSubmit,
        timingData: submissionData.timingData || {}
      })).unwrap();



      // 1. Deactivate anti-cheat measures
      console.log('🔒 Deactivating anti-cheat measures...');
      AntiCheatService.deactivate();

      // 2. Permanently disconnect WebSocket to prevent reconnections
      console.log('🔌 Permanently disconnecting WebSocket...');
      ExamWebSocketService.permanentDisconnect();

      // 3. 🤖 AUTOMATICALLY TRIGGER AI CHECKING AFTER SUCCESSFUL SUBMISSION
      console.log('🤖 Triggering AI checking...');
      try {
        await dispatch(checkExamWithAI({
          examId: examId,
          studentId: null // null for student endpoint
        })).unwrap();
        console.log('✅ AI checking completed');
      } catch (aiError) {
        console.warn('⚠️ AI checking failed:', aiError);
        // Don't block navigation if AI checking fails
      }

      // 4. Navigate to success page
      navigate(`/student/exam-submitted/${examId}`, {
        state: {
          examTitle: examSession.examData?.title,
          submissionTime: new Date().toISOString(),
          submissionResult: result,
          answers: submissionData.answers,
          timingData: submissionData.timingData,
          autoAITriggered: true // Flag to indicate AI was auto-triggered
        }
      });

      // 5. Cleanup answer manager
      answerManager.cleanup();

      console.log('✅ Exam submission complete - all security measures disabled, WebSocket disconnected');

    } catch (error) {

      // Show error but don't navigate away
    }
  }, [examSession, dispatch, token, navigate, examId, answerManager]);

  /**
   * Handle page visibility changes for timing accuracy
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        answerManager.pauseTiming();

      } else {
        answerManager.resumeTiming();

      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [answerManager]);

  /**
   * Timer update handler
   */
  const handleTimerUpdate = useCallback((remainingSeconds) => {
    dispatch(setRemainingTime(remainingSeconds));

    // Auto-submit when time expires (only if exam is active)
    if (remainingSeconds <= 0 && !examSession.isSubmitted && examSession.status === 'active') {
      console.log('Time expired, auto-submitting exam');
      handleSubmitExam(true); // true = auto-submit due to time expiry
    }
  }, [dispatch, examSession.isSubmitted, examSession.status, handleSubmitExam]);

  /**
   * Initialize exam on component mount
   */
  useEffect(() => {
    initializeExam();
  }, [initializeExam]);

  /**
   * Setup WebSocket and anti-cheat when session is active
   */
  useEffect(() => {
    if (examSession.status === 'active' && examSession.sessionId) {
      setupWebSocketListeners();
      setupAntiCheating();
      
      return () => {
        // Cleanup on unmount
        ExamWebSocketService.removeAllListeners();
        AntiCheatService.deactivate();
      };
    }
  }, [examSession.status, examSession.sessionId, setupWebSocketListeners, setupAntiCheating]);

  /**
   * Handle auto-submit when strikes reach maximum
   */
  useEffect(() => {
    if (examSession.autoSubmitTriggered && !examSession.isSubmitted) {
      console.log('🚨 Auto-submitting exam due to maximum violations reached');
      handleSubmitExam(true); // true = auto-submit due to violations
    }
  }, [examSession.autoSubmitTriggered, examSession.isSubmitted, handleSubmitExam]);

  /**
   * Handle page unload
   */
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (examSession.status === 'active' && !examSession.isSubmitted) {
        e.preventDefault();
        e.returnValue = 'Are you sure you want to leave? Your exam progress may be lost.';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [examSession.status, examSession.isSubmitted]);

  // 🚨 SECURITY: Show loading state OR if WebSocket is not connected
  if (examSession.loading || !isInitialized || examSession.connectionStatus !== 'connected') {
    const getLoadingMessage = () => {
      if (examSession.loading) return "Initializing Exam Session...";
      if (!isInitialized) return "Preparing Exam Environment...";
      if (examSession.connectionStatus === 'connecting') return "Establishing Secure Connection...";
      if (examSession.connectionStatus === 'disconnected') return "Reconnecting to Exam Server...";
      if (examSession.connectionStatus === 'error') return "Connection Error - Retrying...";
      return "Securing Exam Session...";
    };

    const getLoadingSubtext = () => {
      if (examSession.connectionStatus !== 'connected') {
        return "🔒 For security reasons, the exam content will only be displayed once a secure connection is established.";
      }
      return "Please wait while we prepare your exam session...";
    };

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{getLoadingMessage()}</h2>
          <p className="text-gray-600 mb-4">{getLoadingSubtext()}</p>

          {/* Connection Status Indicator */}
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              examSession.connectionStatus === 'connected' ? 'bg-green-500' :
              examSession.connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span className="text-gray-500">
              Connection: {examSession.connectionStatus || 'initializing'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (examSession.error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Exam Error</h2>
          <p className="text-gray-600 mb-4">{examSession.error}</p>
          <button
            onClick={() => navigate('/student/dashboard')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Show disqualification state
  if (examSession.isDisqualified) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FiShield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-900 mb-2">Exam Disqualified</h2>
          <p className="text-red-700 mb-4">{examSession.disqualificationReason}</p>
          <p className="text-sm text-red-600">You will be redirected to the dashboard shortly.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className={`shadow-sm border-b ${competitionMode ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Exam Info */}
            <div className="flex items-center space-x-4">
              {competitionMode && (
                <div className="flex items-center space-x-2">
                  <FiAward className="h-5 w-5 text-yellow-300" />
                  <span className="text-sm font-medium text-purple-100">COMPETITION</span>
                </div>
              )}
              <h1 className={`text-lg font-semibold ${competitionMode ? 'text-white' : 'text-gray-900'}`}>
                {examSession.examData?.title || exam?.title || 'Exam'}
              </h1>
              <div className={`flex items-center space-x-2 text-sm ${competitionMode ? 'text-purple-100' : 'text-gray-600'}`}>
                <span>Question {examSession.currentQuestionIndex + 1} of {examSession.questions.length}</span>
                <span>•</span>
                <span>{Math.round(progress)}% Complete</span>
              </div>
            </div>

            {/* Status Indicators */}
            <div className="flex items-center space-x-4">
              <ConnectionStatus status={examSession.connectionStatus} />
              
              {/* Security Status */}
              <div className="flex items-center space-x-1 text-sm">
                <FiShield className={`h-4 w-4 ${examSession.strikes > 0 ? 'text-red-500' : 'text-green-500'}`} />
                <span className={examSession.strikes > 0 ? 'text-red-600' : 'text-green-600'}>
                  {examSession.strikes}/3 Strikes
                </span>
              </div>

              {/* Timer */}
              <ExamTimer
                remainingTime={examSession.remainingTime}
                onTimeUpdate={handleTimerUpdate}
                isActive={examSession.status === 'active'}
              />

              {/* Auto-save indicator */}
              {lastSaveTime && (
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <FiSave className="h-4 w-4" />
                  <span>Saved</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Question Display */}
          <div className="lg:col-span-3">
            {/* Debug info - remove in production */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
                <div>Current Question Index: {examSession.currentQuestionIndex}</div>
                <div>Questions Length: {examSession.questions?.length || exam?.questions?.length || 0}</div>
                <div>Current Question: {currentQuestion ? 'Found' : 'Not Found'}</div>
                <div>Question Type: {currentQuestion?.Type || currentQuestion?.question_type || 'Unknown'}</div>
              </div>
            )}

            <QuestionDisplay
              question={currentQuestion || (exam?.questions && exam.questions[examSession.currentQuestionIndex])}
              answer={answerManager.getAnswer(currentQuestion?.id, true)} // Get simple format for display
              onAnswerChange={(answer) => {
                const questionId = currentQuestion?.id || (exam?.questions && exam.questions[examSession.currentQuestionIndex]?.id);
                handleAnswerUpdate(questionId, answer);
                // Start timing for this question if not already started
                if (questionId) {
                  handleQuestionNavigation(questionId);
                }
              }}
              isReadOnly={examSession.isSubmitted || examSession.isDisqualified}
            />
          </div>

          {/* Navigation Sidebar */}
          <div className="lg:col-span-1">
            <ExamNavigation
              questions={examSession.questions || exam?.questions || []}
              answers={examSession.currentAnswers || examSession.answers || {}}
              currentIndex={examSession.currentQuestionIndex}
              onQuestionSelect={(index) => dispatch(setCurrentQuestion(index))}
              onSubmit={() => setShowSubmitConfirm(true)}
              isSubmitting={examSession.isSubmitting}
              canSubmit={!examSession.isSubmitted && !examSession.isDisqualified}
            />
          </div>
        </div>
      </main>

      {/* Security Warnings Modal */}
      <SecurityWarnings
        isOpen={examSession.showWarningModal}
        warning={examSession.currentWarning}
        onDismiss={() => dispatch(dismissWarning())}
      />

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Submit Exam</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to submit your exam? You won't be able to make changes after submission.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowSubmitConfirm(false);
                  handleSubmitExam();
                }}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
              >
                <FiSend className="h-4 w-4" />
                <span>Submit</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamInterface;
